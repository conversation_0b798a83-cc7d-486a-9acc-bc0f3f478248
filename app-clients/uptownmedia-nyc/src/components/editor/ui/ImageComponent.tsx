"use client";

import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useLexicalNodeSelection } from "@lexical/react/useLexicalNodeSelection";
import { mergeRegister } from "@lexical/utils";
import {
	$getNodeByKey,
	$getSelection,
	$isNodeSelection,
	$setSelection,
	CLICK_COMMAND,
	COMMAND_PRIORITY_LOW,
	DRAGSTART_COMMAND,
	KEY_BACKSPACE_COMMAND,
	KEY_DELETE_COMMAND,
	KEY_ENTER_COMMAND,
	KEY_ESCAPE_COMMAND,
	SELECTION_CHANGE_COMMAND,
	type NodeKey,
} from "lexical";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";

import { $isImageNode } from "../nodes/ImageNode";

const imageCache = new Set();

function useSuspenseImage(src: string) {
	if (!imageCache.has(src)) {
		throw new Promise((resolve) => {
			const img = new window.Image();
			img.src = src;
			img.onload = () => {
				imageCache.add(src);
				resolve(null);
			};
			img.onerror = () => {
				imageCache.add(src);
				resolve(null);
			};
		});
	}
}

interface ImageComponentProps {
	altText: string;
	height: "inherit" | number;
	maxWidth: number;
	nodeKey: NodeKey;
	src: string;
	width: "inherit" | number;
}

export default function ImageComponent({
	src,
	altText,
	nodeKey,
	width,
	height,
	maxWidth,
}: ImageComponentProps): JSX.Element {
	const imageRef = useRef<HTMLImageElement>(null);
	const [isSelected, setSelected, clearSelection] =
		useLexicalNodeSelection(nodeKey);
	const [isResizing, setIsResizing] = useState<boolean>(false);
	const [editor] = useLexicalComposerContext();
	const [selection, setSelection] = useState<any>(null);

	const onDelete = useCallback(
		(payload: KeyboardEvent) => {
			if (isSelected && $isNodeSelection($getSelection())) {
				const event: KeyboardEvent = payload;
				event.preventDefault();
				const node = $getNodeByKey(nodeKey);
				if ($isImageNode(node)) {
					node.remove();
					return true;
				}
			}
			return false;
		},
		[isSelected, nodeKey],
	);

	const onEnter = useCallback(
		(event: KeyboardEvent) => {
			const latestSelection = $getSelection();
			const buttonElem = imageRef.current;
			if (
				isSelected &&
				$isNodeSelection(latestSelection) &&
				latestSelection.getNodes().length === 1
			) {
				if (buttonElem !== null && buttonElem !== document.activeElement) {
					event.preventDefault();
					buttonElem.focus();
					return true;
				}
			}
			return false;
		},
		[isSelected],
	);

	const onEscape = useCallback(
		(event: KeyboardEvent) => {
			if (imageRef.current === document.activeElement) {
				$setSelection(null);
				editor.update(() => {
					setSelected(true);
					const parentRootElement = editor.getRootElement();
					if (parentRootElement !== null) {
						parentRootElement.focus();
					}
				});
				return true;
			}
			return false;
		},
		[editor, setSelected],
	);

	const onClick = useCallback(
		(payload: MouseEvent) => {
			const event = payload;

			if (isResizing) {
				return true;
			}

			if (event.target === imageRef.current) {
				if (event.shiftKey) {
					setSelected(!isSelected);
				} else {
					clearSelection();
					setSelected(true);
				}
				return true;
			}

			return false;
		},
		[isResizing, isSelected, setSelected, clearSelection],
	);

	const onRightClick = useCallback(
		(event: MouseEvent): void => {
			editor.getEditorState().read(() => {
				const latestSelection = $getSelection();
				const domElement = event.target as Element;
				if (
					domElement.tagName === "IMG" &&
					$isNodeSelection(latestSelection) &&
					latestSelection.getNodes().length === 1
				) {
					editor.dispatchCommand(
						CLICK_COMMAND,
						event as MouseEvent,
					);
				}
			});
		},
		[editor],
	);

	useEffect(() => {
		let isMounted = true;
		const rootElement = editor.getRootElement();
		const unregister = mergeRegister(
			editor.registerUpdateListener(({ editorState }) => {
				if (isMounted) {
					setSelection(editorState.read(() => $getSelection()));
				}
			}),
			editor.registerCommand(
				SELECTION_CHANGE_COMMAND,
				(_, activeEditor) => {
					return false;
				},
				COMMAND_PRIORITY_LOW,
			),
			editor.registerCommand<MouseEvent>(
				CLICK_COMMAND,
				onClick,
				COMMAND_PRIORITY_LOW,
			),
			editor.registerCommand(
				DRAGSTART_COMMAND,
				(event) => {
					if (event.target === imageRef.current) {
						// TODO: This is just a temporary workaround for FF to behave like other browsers.
						// Ideally, this handles drag & drop too (and all browsers).
						event.preventDefault();
						return true;
					}
					return false;
				},
				COMMAND_PRIORITY_LOW,
			),
			editor.registerCommand(
				KEY_DELETE_COMMAND,
				onDelete,
				COMMAND_PRIORITY_LOW,
			),
			editor.registerCommand(
				KEY_BACKSPACE_COMMAND,
				onDelete,
				COMMAND_PRIORITY_LOW,
			),
			editor.registerCommand(KEY_ENTER_COMMAND, onEnter, COMMAND_PRIORITY_LOW),
			editor.registerCommand(KEY_ESCAPE_COMMAND, onEscape, COMMAND_PRIORITY_LOW),
		);

		const handleRightClick = (event: MouseEvent) => {
			onRightClick(event);
		};

		if (rootElement !== null) {
			rootElement.addEventListener("contextmenu", handleRightClick);
		}

		return () => {
			isMounted = false;
			unregister();
			if (rootElement !== null) {
				rootElement.removeEventListener("contextmenu", handleRightClick);
			}
		};
	}, [
		clearSelection,
		editor,
		isResizing,
		isSelected,
		nodeKey,
		onDelete,
		onEnter,
		onEscape,
		onClick,
		onRightClick,
		setSelected,
	]);

	useSuspenseImage(src);

	const draggable = isSelected && $isNodeSelection(selection) && !isResizing;
	const isFocused = isSelected || isResizing;

	return (
		<div draggable={draggable}>
			<Image
				className={
					isFocused
						? "focused draggable"
						: "cursor-default"
				}
				src={src}
				alt={altText}
				ref={imageRef}
				width={width === "inherit" ? 0 : width}
				height={height === "inherit" ? 0 : height}
				style={{
					height,
					maxWidth,
					width,
				}}
				draggable={false}
			/>
		</div>
	);
}
