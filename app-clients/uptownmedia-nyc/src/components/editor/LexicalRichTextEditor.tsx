"use client";

import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin";
import { TRANSFORMERS } from "@lexical/markdown";
import LexicalErrorBoundary from "@lexical/react/LexicalErrorBoundary";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { ListItemNode, ListNode } from "@lexical/list";
import { LinkNode, AutoLinkNode } from "@lexical/link";
import { CodeHighlightNode, CodeNode } from "@lexical/code";
import { TableNode, TableCellNode, TableRowNode } from "@lexical/table";
import type { EditorState } from "lexical";

import { ImageNode } from "./nodes/ImageNode";
import ImagePlugin from "./plugins/ImagePlugin";
import ToolbarPlugin from "./plugins/ToolbarPlugin";

const theme = {
	ltr: "ltr",
	rtl: "rtl",
	placeholder: "editor-placeholder",
	paragraph: "editor-paragraph",
	quote: "editor-quote",
	heading: {
		h1: "editor-heading-h1",
		h2: "editor-heading-h2",
		h3: "editor-heading-h3",
		h4: "editor-heading-h4",
		h5: "editor-heading-h5",
	},
	list: {
		nested: {
			listitem: "editor-nested-listitem",
		},
		ol: "editor-list-ol",
		ul: "editor-list-ul",
		listitem: "editor-listitem",
	},
	image: "editor-image",
	link: "editor-link",
	text: {
		bold: "editor-text-bold",
		italic: "editor-text-italic",
		overflowed: "editor-text-overflowed",
		hashtag: "editor-text-hashtag",
		underline: "editor-text-underline",
		strikethrough: "editor-text-strikethrough",
		underlineStrikethrough: "editor-text-underlineStrikethrough",
		code: "editor-text-code",
	},
	code: "editor-code",
	codeHighlight: {
		atrule: "editor-tokenAttr",
		attr: "editor-tokenAttr",
		boolean: "editor-tokenProperty",
		builtin: "editor-tokenSelector",
		cdata: "editor-tokenComment",
		char: "editor-tokenSelector",
		class: "editor-tokenFunction",
		"class-name": "editor-tokenFunction",
		comment: "editor-tokenComment",
		constant: "editor-tokenProperty",
		deleted: "editor-tokenProperty",
		doctype: "editor-tokenComment",
		entity: "editor-tokenOperator",
		function: "editor-tokenFunction",
		important: "editor-tokenVariable",
		inserted: "editor-tokenSelector",
		keyword: "editor-tokenAttr",
		namespace: "editor-tokenVariable",
		number: "editor-tokenProperty",
		operator: "editor-tokenOperator",
		prolog: "editor-tokenComment",
		property: "editor-tokenProperty",
		punctuation: "editor-tokenPunctuation",
		regex: "editor-tokenVariable",
		selector: "editor-tokenSelector",
		string: "editor-tokenSelector",
		symbol: "editor-tokenProperty",
		tag: "editor-tokenProperty",
		url: "editor-tokenOperator",
		variable: "editor-tokenVariable",
	},
};

// Catch any errors that occur during Lexical updates and log them
// or throw them as needed. If you don't throw them, Lexical will
// try to recover gracefully without losing user data.
function onError(error: Error) {
	console.error(error);
}

interface LexicalRichTextEditorProps {
	value?: string;
	onChange?: (editorState: EditorState) => void;
	placeholder?: string;
	className?: string;
	isInvalid?: boolean;
	errorMessage?: string;
}

export default function LexicalRichTextEditor({
	value,
	onChange,
	placeholder = "Enter some rich text...",
	className = "",
	isInvalid = false,
	errorMessage,
}: LexicalRichTextEditorProps) {
	const initialConfig = {
		namespace: "RichTextEditor",
		theme,
		onError,
		nodes: [
			HeadingNode,
			ListNode,
			ListItemNode,
			QuoteNode,
			CodeNode,
			CodeHighlightNode,
			TableNode,
			TableCellNode,
			TableRowNode,
			AutoLinkNode,
			LinkNode,
			ImageNode,
		],
		editorState: value || undefined,
	};

	return (
		<div className={`lexical-editor ${className}`}>
			<LexicalComposer initialConfig={initialConfig}>
				<div className="editor-container">
					<ToolbarPlugin />
					<div className="editor-inner">
						<RichTextPlugin
							contentEditable={
								<ContentEditable
									className={`editor-input ${
										isInvalid ? "border-red-500" : "border-gray-300"
									} border rounded-b-lg p-4 min-h-[200px] focus:outline-none focus:ring-2 focus:ring-blue-500`}
									aria-placeholder={placeholder}
									placeholder={
										<div className="editor-placeholder absolute top-4 left-4 text-gray-400 pointer-events-none">
											{placeholder}
										</div>
									}
								/>
							}
							ErrorBoundary={LexicalErrorBoundary}
						/>
						<OnChangePlugin onChange={onChange || (() => {})} />
						<HistoryPlugin />
						<LinkPlugin />
						<ListPlugin />
						<ImagePlugin />
						<MarkdownShortcutPlugin transformers={TRANSFORMERS} />
					</div>
				</div>
			</LexicalComposer>
			{isInvalid && errorMessage && (
				<div className="text-red-500 text-sm mt-1">{errorMessage}</div>
			)}
		</div>
	);
}
