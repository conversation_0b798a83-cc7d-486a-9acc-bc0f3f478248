@tailwind base;
@tailwind components;

@layer components {
    .section {
      @apply container px-2 pt-8 mx-auto space-y-8;
    }
  }

@tailwind utilities;

/* Import Lexical Editor Styles */
@import '../components/editor/editor.css';

/* Global scrollbar styling */
::-webkit-scrollbar {
	width: 16px; /* Total width of the scrollbar */
}

::-webkit-scrollbar-track {
	background: transparent; /* Transparent track */
	padding: 4px; /* Padding around the scrollbar */
}

::-webkit-scrollbar-thumb {
	background-color: #d3d2d2; /* Clear gray color */
	-webkit-border-radius: 8px; /* Rounded top and bottom */
	border-radius: 8px; /* Rounded top and bottom */
	border: 4px solid transparent; /* Padding around the scrollbar thumb */
	background-clip: padding-box; /* Ensures padding is visible */
}

::-webkit-scrollbar-thumb:hover {
	background-color: #fafafa; /* Slightly darker gray on hover */
}

/* For Firefox */
* {
	scrollbar-width: thin;
	scrollbar-color: #d3d3d3 transparent;
}

*::-webkit-scrollbar-corner {
	background: transparent; /* Transparent corner */
}

